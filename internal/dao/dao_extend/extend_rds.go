package daoExtend

import (
	"context"
	"errors"
	"usersrv/internal/config"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// QueryPlayerExtendRds 查询玩家扩展信息
func QueryPlayerExtendRds(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.PlayerExtendKey(productId, playerId)

	hashMap, err := redisx.GetPlayerCli().HGetAllWithNil(ctx, key).Result()

	if errors.Is(err, redisx.Empty) {
		return nil, redis.Nil
	}

	if err != nil {
		entry.Warnf("redis get err: %v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "p_extend rds")
	}

	if len(hashMap) == 0 {
		return nil, redis.Nil
	}

	ret := model.NewPlayerExtendFromRdsHash(hashMap)

	return ret, nil
}

// UpdatePlayerExtendRds 更新玩家扩展信息
func UpdatePlayerExtendRds(ctx context.Context, productId int32, playerId uint64, data *model.PlayerExtend) error {
	entry := logx.NewLogEntry(ctx)
	key := config.PlayerExtendKey(productId, playerId)

	hashMap := data.ToRedisHash()

	// 使用 pipeline 同时设置数据和过期时间
	pipe := redisx.GetPlayerCli().Pipeline()
	pipe.HSet(ctx, key, hashMap)
	pipe.Expire(ctx, key, config.PLAYER_EXPIRE)
	_, err := pipe.Exec(ctx)
	if err != nil {
		entry.Warnf("redis set err: %v", err)
		return err
	}

	return nil
}

// DeletePlayerExtendRds 删除玩家扩展信息
func DeletePlayerExtendRds(ctx context.Context, productId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	key := config.PlayerExtendKey(productId, playerId)

	err := redisx.GetPlayerCli().Del(ctx, key).Err()
	if err != nil {
		entry.Warnf("redis del err: %v", err)
		return err
	}

	return nil
}
